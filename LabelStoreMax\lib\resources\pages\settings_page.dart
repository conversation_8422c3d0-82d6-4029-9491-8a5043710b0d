// Velvete Store
//
// Created by <PERSON><PERSON>.
// Copyright © 2025, <PERSON><PERSON>. All rights reserved.
//
// This software is proprietary and confidential.
// Unauthorized copying, redistribution, or use of this software, in whole or in part,
// is strictly prohibited without the express written permission of <PERSON><PERSON>.
//
// All intellectual property rights, including copyrights, patents, trademarks,
// and trade secrets, in and to the software are owned by <PERSON><PERSON>.
//
// THE SOFTWARE IS PROVIDED "AS IS" WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED,
// INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A
// PARTICULAR PURPOSE, AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR
// COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES, OR OTHER LIABILITY,
// WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

import 'package:flutter/material.dart';
import '/app/services/auth_service.dart';
import '/resources/pages/auth_options_page.dart';
import '/resources/pages/account_profile_update_page.dart';
import '/resources/pages/wishlist_page_widget.dart';
import '/resources/widgets/account_detail_orders_widget.dart';
import '/app/models/checkout_session.dart';
import '/app/models/customer_address.dart';
import '/resources/widgets/customer_address_input.dart';
import '/resources/widgets/velvete_ui.dart';
import '/resources/widgets/buttons.dart';
import '/resources/widgets/safearea_widget.dart';
import '/resources/themes/styles/design_constants.dart';
import 'package:nylo_framework/nylo_framework.dart';
import 'package:nylo_framework/theme/helper/ny_theme.dart';

class SettingsPage extends NyStatefulWidget {
  static RouteView path = ("/settings", (_) => SettingsPage());

  SettingsPage({super.key}) : super(child: () => _SettingsPageState());
}

class _SettingsPageState extends NyPage<SettingsPage> {
  bool _isLoggedIn = false;
  bool _isDarkMode = false;
  bool _notificationsEnabled = true;

  @override
  get init => () async {
    _isLoggedIn = await AuthService().isLoggedIn();
    _isDarkMode = Theme.of(context).brightness == Brightness.dark;
  };

  @override
  Widget view(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'الإعدادات',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Theme.of(context).appBarTheme.titleTextStyle?.color,
          ),
        ),
        centerTitle: true,
        automaticallyImplyLeading: false,
      ),
      body: SafeAreaWidget(
        child: ListView(
          padding: EdgeInsets.all(16),
          children: [
            // General Settings Section
            _buildSectionHeader('الإعدادات العامة'),
            SizedBox(height: 8),
            
            // Login/Profile Setting
            _buildSettingsTile(
              icon: Icons.person,
              title: _isLoggedIn ? 'الملف الشخصي' : 'تسجيل الدخول',
              subtitle: _isLoggedIn ? 'إدارة معلومات الحساب' : 'قم بتسجيل الدخول للوصول إلى حسابك',
              onTap: () {
                if (_isLoggedIn) {
                  routeTo(AccountProfileUpdatePage.path);
                } else {
                  routeTo(AuthOptionsPage.path);
                }
              },
            ),
            

            
            // Address Management Setting (only show if logged in)
            if (_isLoggedIn)
              _buildSettingsTile(
                icon: Icons.location_on,
                title: 'إدارة العناوين',
                subtitle: 'إضافة وتعديل عناوين التوصيل',
                onTap: () {
                  _navigateToAddressManagement();
                },
              ),

            // Wishlist Setting
            _buildSettingsTile(
              icon: Icons.favorite,
              title: 'قائمة رغباتي',
              subtitle: 'عرض وإدارة المنتجات المفضلة',
              onTap: () {
                routeTo(WishListPageWidget.path);
              },
            ),

            // Orders History Setting
            if (_isLoggedIn)
              _buildSettingsTile(
                icon: Icons.history,
                title: 'تاريخ الطلبات',
                subtitle: 'عرض طلباتك السابقة وحالتها',
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => Scaffold(
                        appBar: AppBar(
                          title: Text('تاريخ الطلبات'),
                          leading: IconButton(
                            icon: Icon(Icons.arrow_back_ios),
                            onPressed: () => Navigator.pop(context),
                          ),
                        ),
                        body: SafeArea(
                          child: AccountDetailOrdersWidget(),
                        ),
                      ),
                    ),
                  );
                },
              ),

            // Notifications Toggle
            _buildToggleTile(
              icon: Icons.notifications,
              title: 'تلقي الإشعارات',
              subtitle: 'تفعيل أو إلغاء الإشعارات',
              value: _notificationsEnabled,
              onChanged: (value) {
                setState(() {
                  _notificationsEnabled = value;
                });
                showToast(
                  title: value ? "تم تفعيل الإشعارات" : "تم إلغاء الإشعارات",
                  description: value ? "ستتلقى الإشعارات الآن" : "لن تتلقى إشعارات",
                  style: ToastNotificationStyleType.success,
                );
              },
            ),
            
            // Dark Mode Toggle
            _buildToggleTile(
              icon: Icons.dark_mode,
              title: 'المظهر',
              subtitle: _isDarkMode ? 'الوضع المظلم' : 'الوضع الفاتح',
              value: _isDarkMode,
              onChanged: (value) {
                setState(() {
                  _isDarkMode = value;
                });
                // Toggle theme using existing theme switching logic
                NyTheme.set(context,
                    id: value ? "default_dark_theme" : "default_light_theme");
                showToast(
                  title: "تم تغيير المظهر",
                  description: value ? "تم التبديل إلى الوضع المظلم" : "تم التبديل إلى الوضع الفاتح",
                  style: ToastNotificationStyleType.success,
                );
              },
            ),
            
            // Language Setting
            _buildSettingsTile(
              icon: Icons.language,
              title: 'اللغة',
              subtitle: 'تغيير لغة التطبيق',
              onTap: () {
                NyLanguageSwitcher.showBottomModal(context);
              },
            ),
            
            SizedBox(height: 24),
            
            // App & Support Section
            _buildSectionHeader('التطبيق والدعم'),
            SizedBox(height: 8),
            
            // Rate App Setting
            _buildSettingsTile(
              icon: Icons.star,
              title: 'تقييم التطبيق',
              subtitle: 'قيم تجربتك مع التطبيق',
              onTap: () {
                showToast(
                  title: "شكراً لك",
                  description: "سيتم توجيهك لتقييم التطبيق قريباً",
                  style: ToastNotificationStyleType.info,
                );
              },
            ),
            
            // Privacy & Terms Setting
            _buildSettingsTile(
              icon: Icons.privacy_tip,
              title: 'الخصوصية والشروط',
              subtitle: 'سياسة الخصوصية وشروط الاستخدام',
              onTap: () {
                showToast(
                  title: "قريباً",
                  description: "ستتوفر صفحة الخصوصية والشروط قريباً",
                  style: ToastNotificationStyleType.info,
                );
              },
            ),
            
            // About Us Setting
            _buildSettingsTile(
              icon: Icons.info,
              title: 'من نحن',
              subtitle: 'معلومات عن Velvete Store',
              onTap: () {
                showToast(
                  title: "Velvete Store",
                  description: "متجرك المفضل للتسوق الإلكتروني",
                  style: ToastNotificationStyleType.info,
                );
              },
            ),
            
            SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 8),
      child: Text(
        title,
        style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: const Color(0xFFB76E79), // Main brand color
            ),
      ),
    );
  }

  Widget _buildSettingsTile({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return Card(
      margin: EdgeInsets.symmetric(vertical: 4),
      elevation: 2,
      child: ListTile(
        leading: Container(
          padding: EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: const Color(0xFFB76E79).withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            color: const Color(0xFFB76E79),
            size: 24,
          ),
        ),
        title: Text(
          title,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
        ),
        subtitle: Text(
          subtitle,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
              ),
        ),
        trailing: Icon(
          Icons.chevron_left,
          color: Colors.grey[400],
        ),
        onTap: onTap,
      ),
    );
  }

  void _navigateToAddressManagement() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => _AddressManagementPage(),
      ),
    );
  }

  Widget _buildToggleTile({
    required IconData icon,
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return Card(
      margin: EdgeInsets.symmetric(vertical: 4),
      elevation: 2,
      child: ListTile(
        leading: Container(
          padding: EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: const Color(0xFFB76E79).withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            color: const Color(0xFFB76E79),
            size: 24,
          ),
        ),
        title: Text(
          title,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
        ),
        subtitle: Text(
          subtitle,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
              ),
        ),
        trailing: Switch(
          value: value,
          onChanged: onChanged,
          activeColor: const Color(0xFFB76E79), // Main brand color
          activeTrackColor: const Color(0xFFB76E79).withValues(alpha: 0.3),
          inactiveThumbColor: Colors.grey[400],
          inactiveTrackColor: const Color(0xFF3F3F56).withValues(alpha: 0.3),
        ),
      ),
    );
  }
}

// Address Management Page Implementation
class _AddressManagementPage extends StatefulWidget {
  @override
  _AddressManagementPageState createState() => _AddressManagementPageState();
}

class _AddressManagementPageState extends State<_AddressManagementPage> {
  List<CustomerAddress> _addresses = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadAddresses();
  }

  _loadAddresses() async {
    setState(() => _isLoading = true);
    _addresses = await CheckoutSession.getInstance.getAddressBook();
    setState(() => _isLoading = false);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(0xFFFEF8F8),
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        title: Text(
          'إدارة العناوين',
          style: TextStyle(
            color: Color(0xFF2E3A59),
            fontWeight: FontWeight.bold,
            fontSize: 18,
          ),
        ),
        centerTitle: true,
        leading: IconButton(
          icon: Icon(Icons.arrow_back_ios, color: Color(0xFF2E3A59)),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: _isLoading
          ? Center(child: CircularProgressIndicator())
          : _addresses.isEmpty
              ? _buildEmptyState()
              : _buildAddressList(),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _addNewAddress,
        backgroundColor: Color(0xFFB76E79),
        icon: Icon(Icons.add, color: Colors.white),
        label: Text(
          'عنوان جديد',
          style: TextStyle(color: Colors.white, fontWeight: FontWeight.w600),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.location_off,
            size: 80,
            color: Colors.grey[400],
          ),
          SizedBox(height: 16),
          Text(
            'لا توجد عناوين محفوظة',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Color(0xFF2E3A59),
            ),
          ),
          SizedBox(height: 8),
          Text(
            'أضف عنوان جديد للبدء',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAddressList() {
    return ListView.builder(
      padding: EdgeInsets.all(16),
      itemCount: _addresses.length,
      itemBuilder: (context, index) {
        final address = _addresses[index];
        return _buildAddressCard(address, index);
      },
    );
  }

  Widget _buildAddressCard(CustomerAddress address, int index) {
    bool isSelected = address.isDefault ?? false;

    return Container(
      margin: EdgeInsets.only(bottom: 12),
      decoration: DesignConstants.cardDecoration(
        backgroundColor: Theme.of(context).cardColor,
        isDark: Theme.of(context).brightness == Brightness.dark,
      ).copyWith(
        border: Border.all(
          color: isSelected ? Theme.of(context).primaryColor : Theme.of(context).dividerColor,
          width: isSelected ? 2 : 1,
        ),
      ),
      child: InkWell(
        onTap: () => _selectAddress(address.addressId!),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Row(
            children: [
              // Selection indicator
              Container(
                width: 24,
                height: 24,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: isSelected ? Color(0xFFFF8C00) : Colors.transparent,
                  border: Border.all(
                    color: isSelected ? Color(0xFFFF8C00) : Colors.grey[400]!,
                    width: 2,
                  ),
                ),
                child: isSelected
                    ? Icon(Icons.check, color: Colors.white, size: 16)
                    : null,
              ),
              SizedBox(width: 16),

              // Address details
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Text(
                          address.addressLabel ?? 'عنوان',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: Color(0xFF2E3A59),
                          ),
                        ),
                        if (isSelected) ...[
                          SizedBox(width: 8),
                          Container(
                            padding: EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                            decoration: BoxDecoration(
                              color: Color(0xFFFF8C00).withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Text(
                              'افتراضي',
                              style: TextStyle(
                                fontSize: 12,
                                color: Color(0xFFFF8C00),
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),
                    SizedBox(height: 4),
                    Text(
                      '${address.firstName} ${address.lastName}',
                      style: TextStyle(
                        fontSize: 14,
                        color: Color(0xFF2E3A59),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    SizedBox(height: 2),
                    Text(
                      address.addressFull(),
                      style: TextStyle(
                        fontSize: 13,
                        color: Colors.grey[600],
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),

              // Action buttons
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  IconButton(
                    onPressed: () => _editAddress(address),
                    icon: Icon(Icons.edit, color: Color(0xFFB76E79), size: 20),
                    padding: EdgeInsets.all(8),
                    constraints: BoxConstraints(minWidth: 32, minHeight: 32),
                  ),
                  IconButton(
                    onPressed: () => _deleteAddress(address.addressId!),
                    icon: Icon(Icons.delete, color: Colors.red, size: 20),
                    padding: EdgeInsets.all(8),
                    constraints: BoxConstraints(minWidth: 32, minHeight: 32),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  _selectAddress(String addressId) async {
    await CheckoutSession.getInstance.setSelectedAddress(addressId);
    _loadAddresses();
  }

  _addNewAddress() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => _AddressFormPage(),
      ),
    ).then((_) => _loadAddresses());
  }

  _editAddress(CustomerAddress address) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => _AddressFormPage(address: address),
      ),
    ).then((_) => _loadAddresses());
  }

  _deleteAddress(String addressId) async {
    bool? confirm = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('حذف العنوان'),
        content: Text('هل أنت متأكد من حذف هذا العنوان؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: Text('حذف', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );

    if (confirm == true) {
      await CheckoutSession.getInstance.deleteAddress(addressId);
      _loadAddresses();
    }
  }
}

// Address Form Page Implementation
class _AddressFormPage extends StatefulWidget {
  final CustomerAddress? address;

  _AddressFormPage({this.address});

  @override
  _AddressFormPageState createState() => _AddressFormPageState();
}

class _AddressFormPageState extends State<_AddressFormPage> {
  final _formKey = GlobalKey<FormState>();
  final _firstNameController = TextEditingController();
  final _lastNameController = TextEditingController();
  final _addressLineController = TextEditingController();
  final _cityController = TextEditingController();
  final _postalCodeController = TextEditingController();
  final _phoneController = TextEditingController();

  String _selectedLabel = 'المنزل';
  final List<String> _addressLabels = ['المنزل', 'العمل', 'أخرى'];
  bool _isDefault = false;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    if (widget.address != null) {
      _populateFields();
    }
  }

  _populateFields() {
    final address = widget.address!;
    _firstNameController.text = address.firstName ?? '';
    _lastNameController.text = address.lastName ?? '';
    _addressLineController.text = address.addressLine ?? '';
    _cityController.text = address.city ?? '';
    _postalCodeController.text = address.postalCode ?? '';
    _phoneController.text = address.phoneNumber ?? '';
    _selectedLabel = address.addressLabel ?? 'المنزل';
    _isDefault = address.isDefault ?? false;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(0xFFFEF8F8),
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        title: Text(
          widget.address == null ? 'إضافة عنوان جديد' : 'تعديل العنوان',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            color: Theme.of(context).colorScheme.onSurface,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
        leading: IconButton(
          icon: Icon(Icons.arrow_back_ios, color: Color(0xFF2E3A59)),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: EdgeInsets.all(16),
          child: Column(
            children: [
              _buildFormCard(),
              SizedBox(height: 20),
              _buildSaveButton(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFormCard() {
    return Container(
      padding: EdgeInsets.all(20),
      decoration: DesignConstants.cardDecoration(
        backgroundColor: Theme.of(context).cardColor,
        isDark: Theme.of(context).brightness == Brightness.dark,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Address Label Selection
          Text(
            'نوع العنوان',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
              color: Theme.of(context).colorScheme.onSurface,
            ),
          ),
          SizedBox(height: 8),
          DropdownButtonFormField<String>(
            value: _selectedLabel,
            decoration: InputDecoration(
              border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
              contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            ),
            items: _addressLabels.map((label) {
              return DropdownMenuItem(value: label, child: Text(label));
            }).toList(),
            onChanged: (value) => setState(() => _selectedLabel = value!),
          ),
          SizedBox(height: 16),

          // Name Fields
          Row(
            children: [
              Expanded(
                child: TextFormField(
                  controller: _firstNameController,
                  decoration: InputDecoration(
                    labelText: 'الاسم الأول',
                    border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
                  ),
                  validator: (value) => value?.isEmpty == true ? 'مطلوب' : null,
                ),
              ),
              SizedBox(width: 12),
              Expanded(
                child: TextFormField(
                  controller: _lastNameController,
                  decoration: InputDecoration(
                    labelText: 'اسم العائلة',
                    border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
                  ),
                  validator: (value) => value?.isEmpty == true ? 'مطلوب' : null,
                ),
              ),
            ],
          ),
          SizedBox(height: 16),

          // Address Line
          TextFormField(
            controller: _addressLineController,
            decoration: InputDecoration(
              labelText: 'العنوان التفصيلي',
              border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
            ),
            validator: (value) => value?.isEmpty == true ? 'مطلوب' : null,
            maxLines: 2,
          ),
          SizedBox(height: 16),

          // City and Postal Code
          Row(
            children: [
              Expanded(
                child: TextFormField(
                  controller: _cityController,
                  decoration: InputDecoration(
                    labelText: 'المدينة',
                    border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
                  ),
                  validator: (value) => value?.isEmpty == true ? 'مطلوب' : null,
                ),
              ),
              SizedBox(width: 12),
              Expanded(
                child: TextFormField(
                  controller: _postalCodeController,
                  decoration: InputDecoration(
                    labelText: 'الرمز البريدي',
                    border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 16),

          // Phone Number
          TextFormField(
            controller: _phoneController,
            decoration: InputDecoration(
              labelText: 'رقم الهاتف',
              border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
              prefixText: '+218 ',
            ),
            keyboardType: TextInputType.phone,
            validator: (value) => value?.isEmpty == true ? 'مطلوب' : null,
          ),
          SizedBox(height: 16),

          // Default Address Toggle
          Row(
            children: [
              Checkbox(
                value: _isDefault,
                onChanged: (value) => setState(() => _isDefault = value ?? false),
                activeColor: Color(0xFFB76E79),
              ),
              Text(
                'جعل هذا العنوان الافتراضي',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).colorScheme.onSurface,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSaveButton() {
    return SizedBox(
      width: double.infinity,
      height: 50,
      child: ElevatedButton(
        onPressed: _isLoading ? null : _saveAddress,
        style: ElevatedButton.styleFrom(
          backgroundColor: Color(0xFFB76E79),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: _isLoading
            ? CircularProgressIndicator(color: Colors.white)
            : Text(
                widget.address == null ? 'حفظ العنوان' : 'تحديث العنوان',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
      ),
    );
  }

  _saveAddress() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      CustomerAddress address = CustomerAddress(
        addressId: widget.address?.addressId,
        firstName: _firstNameController.text,
        lastName: _lastNameController.text,
        addressLine: _addressLineController.text,
        city: _cityController.text,
        postalCode: _postalCodeController.text,
        phoneNumber: _phoneController.text,
        addressLabel: _selectedLabel,
        isDefault: _isDefault,
      );

      if (widget.address == null) {
        // Add new address
        await CheckoutSession.getInstance.addNewAddress(address);
      } else {
        // Update existing address
        List<CustomerAddress> addresses = await CheckoutSession.getInstance.getAddressBook();
        int index = addresses.indexWhere((addr) => addr.addressId == widget.address!.addressId);
        if (index != -1) {
          addresses[index] = address;
          await CheckoutSession.getInstance.saveAddressBook(addresses);
        }
      }

      Navigator.pop(context);
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('حدث خطأ أثناء حفظ العنوان')),
      );
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  void dispose() {
    _firstNameController.dispose();
    _lastNameController.dispose();
    _addressLineController.dispose();
    _cityController.dispose();
    _postalCodeController.dispose();
    _phoneController.dispose();
    super.dispose();
  }
}
