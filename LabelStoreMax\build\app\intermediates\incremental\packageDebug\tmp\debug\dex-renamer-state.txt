#Mon Jul 21 02:06:20 EET 2025
base.0=C\:\\Users\\HP\\Downloads\\Velvete\\APP\\Project 01\\flutter_velvete_1\\LabelStoreMax\\build\\app\\intermediates\\dex\\debug\\mergeExtDexDebug\\classes.dex
base.1=C\:\\Users\\HP\\Downloads\\Velvete\\APP\\Project 01\\flutter_velvete_1\\LabelStoreMax\\build\\app\\intermediates\\dex\\debug\\mergeLibDexDebug\\0\\classes.dex
base.2=C\:\\Users\\HP\\Downloads\\Velvete\\APP\\Project 01\\flutter_velvete_1\\LabelStoreMax\\build\\app\\intermediates\\dex\\debug\\mergeProjectDexDebug\\0\\classes.dex
base.3=C\:\\Users\\HP\\Downloads\\Velvete\\APP\\Project 01\\flutter_velvete_1\\LabelStoreMax\\build\\app\\intermediates\\dex\\debug\\mergeProjectDexDebug\\1\\classes.dex
base.4=C\:\\Users\\HP\\Downloads\\Velvete\\APP\\Project 01\\flutter_velvete_1\\LabelStoreMax\\build\\app\\intermediates\\desugar_lib_dex\\debug\\l8DexDesugarLibDebug\\classes1000.dex
base.5=C\:\\Users\\HP\\Downloads\\Velvete\\APP\\Project 01\\flutter_velvete_1\\LabelStoreMax\\build\\app\\intermediates\\dex\\debug\\mergeExtDexDebug\\classes2.dex
base.6=C\:\\Users\\HP\\Downloads\\Velvete\\APP\\Project 01\\flutter_velvete_1\\LabelStoreMax\\build\\app\\intermediates\\dex\\debug\\mergeExtDexDebug\\classes3.dex
base.7=C\:\\Users\\HP\\Downloads\\Velvete\\APP\\Project 01\\flutter_velvete_1\\LabelStoreMax\\build\\app\\intermediates\\dex\\debug\\mergeExtDexDebug\\classes4.dex
base.8=C\:\\Users\\HP\\Downloads\\Velvete\\APP\\Project 01\\flutter_velvete_1\\LabelStoreMax\\build\\app\\intermediates\\dex\\debug\\mergeExtDexDebug\\classes5.dex
path.0=classes.dex
path.1=0/classes.dex
path.2=0/classes.dex
path.3=1/classes.dex
path.4=classes1000.dex
path.5=classes2.dex
path.6=classes3.dex
path.7=classes4.dex
path.8=classes5.dex
renamed.0=classes.dex
renamed.1=classes2.dex
renamed.2=classes3.dex
renamed.3=classes4.dex
renamed.4=classes5.dex
renamed.5=classes6.dex
renamed.6=classes7.dex
renamed.7=classes8.dex
renamed.8=classes9.dex
